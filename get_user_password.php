<?php
session_start();
include "db.php";

// Security check: Only logged-in admin and staff users can access this endpoint
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || !in_array($_SESSION['role'], ['admin', 'super admin', 'staff'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit();
}

// Validate the user ID parameter
if (!isset($_GET['user_id']) || !is_numeric($_GET['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid user ID']);
    exit();
}

$userId = intval($_GET['user_id']);

// First, get user details
$user_sql = "SELECT username, password FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_sql);
$user_stmt->bind_param("i", $userId);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'User not found']);
    exit();
}

$user_data = $user_result->fetch_assoc();

// Check if we have a password records table for plain passwords
$check_table = $conn->query("SHOW TABLES LIKE 'password_records'");
$has_password_records = $check_table->num_rows > 0;

// Attempt to fetch the user password from password_records
if ($has_password_records) {
    // If we have a password records table, try to get the latest plain password
    $password_sql = "SELECT plain_password FROM password_records WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
    $password_stmt = $conn->prepare($password_sql);
    $password_stmt->bind_param("i", $userId);
    $password_stmt->execute();
    $password_result = $password_stmt->get_result();
    
    if ($password_result->num_rows > 0) {
        $password_row = $password_result->fetch_assoc();
        $password = $password_row['plain_password'];
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'password' => $password]);
        exit();
    }
}

// If the password_records table doesn't exist or no record was found, 
// we need to create it
if (!$has_password_records) {
    // Create the table
    $create_table_sql = "CREATE TABLE IF NOT EXISTS password_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plain_password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if (!$conn->query($create_table_sql)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Failed to create password records table']);
        exit();
    }
}

// No stored password was found, so we'll return a status message
header('Content-Type: application/json');
echo json_encode([
    'success' => true, 
    'password' => null, 
    'message' => 'Password is securely hashed and cannot be retrieved. Please set a new password.'
]);

$conn->close(); 